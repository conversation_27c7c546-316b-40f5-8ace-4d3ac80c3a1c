#!/usr/bin/env python3
"""
Test script for the new TimelineDiagram implementation.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render import Me<PERSON><PERSON><PERSON><PERSON>, TimelineDiagram

def test_basic_timeline():
    """Test basic timeline creation."""
    print("Testing basic timeline creation...")
    
    timeline = TimelineDiagram(title="Project Timeline")
    
    # Add some periods and events
    timeline.add_event("2021", "Project started")
    timeline.add_event("2022", "First milestone")
    timeline.add_event("2022", "Second milestone")
    timeline.add_event("2023", "Project completed")
    
    mermaid_code = timeline.to_mermaid()
    print("Generated Mermaid code:")
    print(mermaid_code)
    print()
    
    expected_elements = ["timeline", "title Project Timeline", "2021", "2022", "2023"]
    for element in expected_elements:
        if element not in mermaid_code:
            print(f"❌ Missing expected element: {element}")
            return False
    
    print("✅ Basic timeline creation successful!")
    return True

def test_timeline_with_sections():
    """Test timeline with sections."""
    print("Testing timeline with sections...")
    
    timeline = TimelineDiagram(title="Software Development Lifecycle")
    
    # Add planning section
    planning = timeline.add_section("Planning Phase")
    q1 = planning.add_period("Q1 2024")
    q1.add_event("Requirements gathering")
    q1.add_event("Architecture design")
    
    q2 = planning.add_period("Q2 2024")
    q2.add_event("Technical specifications")
    
    # Add development section
    development = timeline.add_section("Development Phase")
    q3 = development.add_period("Q3 2024")
    q3.add_event("Core development")
    q3.add_event("Unit testing")
    
    q4 = development.add_period("Q4 2024")
    q4.add_event("Integration testing")
    q4.add_event("Bug fixes")
    
    mermaid_code = timeline.to_mermaid()
    print("Generated Mermaid code:")
    print(mermaid_code)
    print()
    
    expected_elements = [
        "timeline",
        "title Software Development Lifecycle",
        "section Planning Phase",
        "section Development Phase",
        "Q1 2024",
        "Q2 2024",
        "Q3 2024",
        "Q4 2024",
        "Requirements gathering",
        "Core development"
    ]
    
    for element in expected_elements:
        if element not in mermaid_code:
            print(f"❌ Missing expected element: {element}")
            return False
    
    print("✅ Timeline with sections successful!")
    return True

def test_timeline_rendering():
    """Test timeline rendering with the renderer."""
    print("Testing timeline rendering...")
    
    renderer = MermaidRenderer()
    
    # Create a timeline
    timeline = TimelineDiagram(title="Company History")
    
    # Add events
    timeline.add_event("1990", "Company founded")
    timeline.add_event("1995", "First product launch")
    timeline.add_event("2000", "IPO")
    timeline.add_event("2010", "International expansion")
    timeline.add_event("2020", "Digital transformation")
    
    try:
        # Test SVG rendering
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ SVG rendering successful! Content length: {len(svg_content)}")
        
        # Test PNG rendering
        png_content = renderer.render(timeline, format="png")
        print(f"✅ PNG rendering successful! Content length: {len(png_content)} bytes")
        
        return True
    except Exception as e:
        print(f"❌ Timeline rendering failed: {e}")
        return False

def test_timeline_save():
    """Test saving timeline to files."""
    print("Testing timeline save functionality...")
    
    renderer = MermaidRenderer()
    
    # Create a timeline
    timeline = TimelineDiagram(title="Technology Evolution")
    
    # Add a section with multiple events
    internet = timeline.add_section("Internet Era")
    internet.add_period("1990s").add_event("World Wide Web")
    internet.add_period("2000s").add_event("Social Media")
    
    mobile = timeline.add_section("Mobile Era")
    mobile.add_period("2010s").add_event("Smartphones")
    mobile.add_period("2020s").add_event("5G Networks")
    
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    success_count = 0
    
    # Test SVG save
    try:
        renderer.save(timeline, output_dir / "timeline.svg", format="svg")
        print("✅ SVG save successful!")
        success_count += 1
    except Exception as e:
        print(f"❌ SVG save failed: {e}")
    
    # Test PNG save
    try:
        renderer.save(timeline, output_dir / "timeline.png", format="png")
        print("✅ PNG save successful!")
        success_count += 1
    except Exception as e:
        print(f"❌ PNG save failed: {e}")
    
    return success_count >= 1  # At least one format should work

def test_timeline_convenience_methods():
    """Test convenience methods for timeline creation."""
    print("Testing timeline convenience methods...")
    
    timeline = TimelineDiagram()
    
    # Test add_event convenience method
    timeline.add_event("Phase 1", "Planning")
    timeline.add_event("Phase 1", "Design")  # Should add to existing period
    timeline.add_event("Phase 2", "Development")
    
    mermaid_code = timeline.to_mermaid()
    print("Generated Mermaid code:")
    print(mermaid_code)
    print()
    
    # Check that Phase 1 has multiple events
    lines = mermaid_code.split('\n')
    phase1_lines = [line for line in lines if 'Phase 1' in line or ('Planning' in line or 'Design' in line)]
    
    if len(phase1_lines) < 2:
        print("❌ Convenience method failed - Phase 1 should have multiple events")
        return False
    
    print("✅ Timeline convenience methods successful!")
    return True

def main():
    """Run all timeline tests."""
    print("=== Testing TimelineDiagram Implementation ===\n")
    
    tests = [
        ("Basic Timeline Creation", test_basic_timeline),
        ("Timeline with Sections", test_timeline_with_sections),
        ("Timeline Rendering", test_timeline_rendering),
        ("Timeline Save Functionality", test_timeline_save),
        ("Timeline Convenience Methods", test_timeline_convenience_methods),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"=== Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        print("🎉 All timeline tests passed! TimelineDiagram implementation is working correctly.")
    elif passed >= 3:
        print("✅ Core timeline functionality is working. Some advanced features may need attention.")
    else:
        print("⚠️  Timeline implementation needs work. Check the error messages above.")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
