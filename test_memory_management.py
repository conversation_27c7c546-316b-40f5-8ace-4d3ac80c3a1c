#!/usr/bin/env python3
"""
Test script for memory management enhancements.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render import TimelineDiagram

def test_caching_functionality():
    """Test that caching works correctly."""
    print("Testing caching functionality...")
    
    timeline = TimelineDiagram(title="Test Timeline")
    timeline.add_event("2021", "Event 1")
    
    # First call should generate and cache
    mermaid1 = timeline.to_mermaid()
    
    # Second call should use cache (same object)
    mermaid2 = timeline.to_mermaid()
    
    if mermaid1 is not mermaid2:
        print("❌ Caching failed - different objects returned")
        return False
    
    if mermaid1 != mermaid2:
        print("❌ Caching failed - different content returned")
        return False
    
    print("✅ Caching functionality working correctly!")
    return True

def test_cache_invalidation():
    """Test that cache is invalidated when diagram changes."""
    print("Testing cache invalidation...")
    
    timeline = TimelineDiagram(title="Test Timeline")
    timeline.add_event("2021", "Event 1")
    
    # Get initial cached version
    mermaid1 = timeline.to_mermaid()
    
    # Modify the diagram
    timeline.add_event("2022", "Event 2")
    
    # Get new version (should be different)
    mermaid2 = timeline.to_mermaid()
    
    if mermaid1 == mermaid2:
        print("❌ Cache invalidation failed - same content after modification")
        return False
    
    if "Event 2" not in mermaid2:
        print("❌ Cache invalidation failed - new content not present")
        return False
    
    print("✅ Cache invalidation working correctly!")
    return True

def test_manual_cache_clearing():
    """Test manual cache clearing."""
    print("Testing manual cache clearing...")
    
    timeline = TimelineDiagram(title="Test Timeline")
    timeline.add_event("2021", "Event 1")
    
    # Generate and cache
    mermaid1 = timeline.to_mermaid()
    
    # Clear cache manually
    timeline.clear_cache()
    
    # Get new version (should be regenerated)
    mermaid2 = timeline.to_mermaid()
    
    # Content should be the same but objects should be different
    if mermaid1 != mermaid2:
        print("❌ Manual cache clearing failed - content changed")
        return False
    
    if mermaid1 is mermaid2:
        print("❌ Manual cache clearing failed - same object returned")
        return False
    
    print("✅ Manual cache clearing working correctly!")
    return True

def test_disposal_functionality():
    """Test diagram disposal functionality."""
    print("Testing disposal functionality...")
    
    timeline = TimelineDiagram(title="Test Timeline")
    timeline.add_event("2021", "Event 1")
    
    # Should work normally before disposal
    try:
        mermaid_code = timeline.to_mermaid()
        if "Event 1" not in mermaid_code:
            print("❌ Diagram not working before disposal")
            return False
    except Exception as e:
        print(f"❌ Unexpected error before disposal: {e}")
        return False
    
    # Dispose the diagram
    timeline.dispose()
    
    # Should raise error after disposal
    try:
        timeline.to_mermaid()
        print("❌ Disposal failed - to_mermaid() still works")
        return False
    except RuntimeError as e:
        if "disposed" not in str(e).lower():
            print(f"❌ Wrong error message: {e}")
            return False
    except Exception as e:
        print(f"❌ Wrong exception type: {e}")
        return False
    
    # Should raise error when trying to add events
    try:
        timeline.add_event("2022", "Event 2")
        print("❌ Disposal failed - add_event() still works")
        return False
    except RuntimeError as e:
        if "disposed" not in str(e).lower():
            print(f"❌ Wrong error message: {e}")
            return False
    except Exception as e:
        print(f"❌ Wrong exception type: {e}")
        return False
    
    print("✅ Disposal functionality working correctly!")
    return True

def test_config_cache_invalidation():
    """Test that cache is invalidated when configuration changes."""
    print("Testing configuration cache invalidation...")
    
    timeline = TimelineDiagram(title="Test Timeline")
    timeline.add_event("2021", "Event 1")
    
    # Get initial cached version
    mermaid1 = timeline.to_mermaid()
    
    # Change configuration
    timeline.add_config("test_key", "test_value")
    
    # Get new version (should be regenerated due to config change)
    mermaid2 = timeline.to_mermaid()
    
    # Content should be the same but objects should be different
    if mermaid1 != mermaid2:
        print("❌ Config cache invalidation failed - content changed unexpectedly")
        return False
    
    if mermaid1 is mermaid2:
        print("❌ Config cache invalidation failed - same object returned")
        return False
    
    print("✅ Configuration cache invalidation working correctly!")
    return True

def test_multiple_disposal():
    """Test that multiple disposal calls don't cause issues."""
    print("Testing multiple disposal calls...")
    
    timeline = TimelineDiagram(title="Test Timeline")
    timeline.add_event("2021", "Event 1")
    
    # Dispose multiple times
    try:
        timeline.dispose()
        timeline.dispose()  # Should not cause issues
        timeline.dispose()  # Should not cause issues
        print("✅ Multiple disposal calls handled correctly!")
        return True
    except Exception as e:
        print(f"❌ Multiple disposal calls failed: {e}")
        return False

def main():
    """Run all memory management tests."""
    print("=== Testing Memory Management Enhancements ===\n")
    
    tests = [
        ("Caching Functionality", test_caching_functionality),
        ("Cache Invalidation", test_cache_invalidation),
        ("Manual Cache Clearing", test_manual_cache_clearing),
        ("Disposal Functionality", test_disposal_functionality),
        ("Config Cache Invalidation", test_config_cache_invalidation),
        ("Multiple Disposal", test_multiple_disposal),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"=== Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        print("🎉 All memory management tests passed! Implementation is working correctly.")
    elif passed >= 4:
        print("✅ Core memory management functionality is working. Some advanced features may need attention.")
    else:
        print("⚠️  Memory management implementation needs work. Check the error messages above.")
    
    return passed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
