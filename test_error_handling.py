#!/usr/bin/env python3
"""
Test script for enhanced error handling functionality.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render.exceptions import (
    MermaidRenderError, ValidationError, RenderingError, 
    UnsupportedFormatError, DiagramError, ErrorAggregator
)

def test_base_error_enhancements():
    """Test enhanced base error functionality."""
    print("Testing base error enhancements...")
    
    # Test basic error with code and suggestions
    error = MermaidRenderError(
        "Test error message",
        error_code="TEST_ERROR",
        suggestions=["Try this fix", "Or try this alternative"],
        details={"key1": "value1", "key2": "value2"}
    )
    
    error_str = str(error)
    if "TEST_ERROR" not in error_str:
        print("❌ Error code not in string representation")
        return False
    
    if "Try this fix" not in error_str:
        print("❌ Suggestions not in string representation")
        return False
    
    if "key1: value1" not in error_str:
        print("❌ Details not in string representation")
        return False
    
    # Test to_dict method
    error_dict = error.to_dict()
    if error_dict["error_code"] != "TEST_ERROR":
        print("❌ Error code not in dictionary representation")
        return False
    
    if len(error_dict["suggestions"]) != 2:
        print("❌ Suggestions not properly serialized")
        return False
    
    print("✅ Base error enhancements working correctly!")
    return True

def test_validation_error_suggestions():
    """Test automatic suggestion generation for validation errors."""
    print("Testing validation error suggestions...")
    
    # Test bracket error suggestions
    error = ValidationError(
        "Invalid diagram syntax",
        errors=["Unmatched brackets in line 3"],
        line_number=3
    )
    
    error_str = str(error)
    if "brackets are properly closed" not in error_str:
        print("❌ Bracket suggestion not generated")
        return False
    
    # Test no nodes error suggestions
    error2 = ValidationError("No nodes found in flowchart")
    error_str2 = str(error2)
    if "Add at least one node" not in error_str2:
        print("❌ No nodes suggestion not generated")
        return False
    
    # Test unknown diagram type suggestions
    error3 = ValidationError("Unknown diagram type: flowchrt")
    error_str3 = str(error3)
    if "supported diagram type" not in error_str3:
        print("❌ Unknown diagram type suggestion not generated")
        return False
    
    print("✅ Validation error suggestions working correctly!")
    return True

def test_rendering_error_suggestions():
    """Test automatic suggestion generation for rendering errors."""
    print("Testing rendering error suggestions...")
    
    # Test network error suggestions
    error = RenderingError(
        "Network request failed",
        status_code=404
    )
    
    error_str = str(error)
    if "service URL is correct" not in error_str:
        print("❌ Network error suggestion not generated")
        return False
    
    # Test PDF XML error suggestions
    error2 = RenderingError(
        "PDF rendering failed: XML parsing error",
        format="pdf"
    )
    
    error_str2 = str(error2)
    if "SVG or PNG format" not in error_str2:
        print("❌ PDF XML error suggestion not generated")
        return False
    
    # Test timeout error suggestions
    error3 = RenderingError("Request timeout occurred")
    error_str3 = str(error3)
    if "timeout value" not in error_str3:
        print("❌ Timeout error suggestion not generated")
        return False
    
    print("✅ Rendering error suggestions working correctly!")
    return True

def test_unsupported_format_suggestions():
    """Test automatic suggestion generation for unsupported format errors."""
    print("Testing unsupported format suggestions...")
    
    # Test format suggestions
    error = UnsupportedFormatError(
        "Format not supported",
        requested_format="jpg",
        supported_formats=["svg", "png", "pdf"]
    )
    
    error_str = str(error)
    if "svg, png, pdf" not in error_str:
        print("❌ Supported formats not listed")
        return False
    
    if "png" not in error_str or "raster images" not in error_str:
        print("❌ JPG to PNG suggestion not generated")
        return False
    
    # Test HTML to SVG suggestion
    error2 = UnsupportedFormatError(
        "Format not supported",
        requested_format="html",
        supported_formats=["svg", "png"]
    )
    
    error_str2 = str(error2)
    if "svg" not in error_str2 or "embedded in HTML" not in error_str2:
        print("❌ HTML to SVG suggestion not generated")
        return False
    
    print("✅ Unsupported format suggestions working correctly!")
    return True

def test_diagram_error_functionality():
    """Test diagram error functionality."""
    print("Testing diagram error functionality...")
    
    # Test disposed diagram error
    error = DiagramError(
        "Cannot use diagram after it has been disposed",
        diagram_type="flowchart",
        operation="add_node"
    )
    
    error_str = str(error)
    if "new diagram instance" not in error_str:
        print("❌ Disposed diagram suggestion not generated")
        return False
    
    # Test duplicate node error
    error2 = DiagramError(
        "Duplicate node ID found",
        diagram_type="flowchart",
        operation="add_node"
    )
    
    error_str2 = str(error2)
    if "unique node IDs" not in error_str2:
        print("❌ Duplicate node suggestion not generated")
        return False
    
    print("✅ Diagram error functionality working correctly!")
    return True

def test_error_aggregator():
    """Test error aggregator functionality."""
    print("Testing error aggregator...")
    
    aggregator = ErrorAggregator()
    
    # Should start empty
    if aggregator.has_errors() or aggregator.has_warnings():
        print("❌ Aggregator not empty on initialization")
        return False
    
    # Add some errors and warnings
    aggregator.add_error(ValidationError("Test validation error"))
    aggregator.add_error(RenderingError("Test rendering error"))
    aggregator.add_warning("Test warning")
    
    if not aggregator.has_errors() or not aggregator.has_warnings():
        print("❌ Aggregator not detecting added errors/warnings")
        return False
    
    # Test summary
    summary = aggregator.get_summary()
    if summary["error_count"] != 2 or summary["warning_count"] != 1:
        print("❌ Aggregator summary counts incorrect")
        return False
    
    # Test raise_if_errors
    try:
        aggregator.raise_if_errors("Test multiple errors")
        print("❌ Aggregator should have raised an exception")
        return False
    except MermaidRenderError as e:
        if "MULTIPLE_ERRORS" not in str(e):
            print("❌ Aggregator exception missing error code")
            return False
    
    print("✅ Error aggregator working correctly!")
    return True

def main():
    """Run all error handling tests."""
    print("=== Testing Enhanced Error Handling ===\n")
    
    tests = [
        ("Base Error Enhancements", test_base_error_enhancements),
        ("Validation Error Suggestions", test_validation_error_suggestions),
        ("Rendering Error Suggestions", test_rendering_error_suggestions),
        ("Unsupported Format Suggestions", test_unsupported_format_suggestions),
        ("Diagram Error Functionality", test_diagram_error_functionality),
        ("Error Aggregator", test_error_aggregator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"=== Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        print("🎉 All error handling tests passed! Enhanced error handling is working correctly.")
    elif passed >= 4:
        print("✅ Core error handling enhancements are working. Some advanced features may need attention.")
    else:
        print("⚠️  Error handling enhancements need work. Check the error messages above.")
    
    return passed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
